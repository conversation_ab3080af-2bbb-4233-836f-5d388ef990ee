using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Bdo.Ess.CtsIntegration.Certificate;
using Bdo.Ess.CtsIntegration.Entities;
using Shouldly;
using Volo.Abp.Domain.Repositories;
using Xunit;

namespace Bdo.Ess.CtsIntegration.EntityFrameworkCore.Applications;

/// <summary>
/// EF Core integration tests for CTS Package Generation functionality.
/// Tests database operations, transactions, and data persistence with real SQLite database.
/// Covers comprehensive scenarios including CRUD operations, error handling, and business rules.
/// This demonstrates the typical EF Core test patterns with 15+ test cases.
/// </summary>
public class EfCoreCtsPackageGenerationAppService_Tests : CtsIntegrationEntityFrameworkCoreTestBase
{
    private readonly IRepository<CtsPackageRequest, Guid> _packageRequestRepository;
    private readonly IRepository<BahamasCertificate, Guid> _certificateRepository;

    public EfCoreCtsPackageGenerationAppService_Tests()
    {
        // Get real repositories from DI container for EF Core integration testing
        _packageRequestRepository = GetRequiredService<IRepository<CtsPackageRequest, Guid>>();
        _certificateRepository = GetRequiredService<IRepository<BahamasCertificate, Guid>>();
    }

    #region Database CRUD Operations Tests

    [Fact]
    public async Task Create_CtsPackageRequest_Should_Persist_To_Database()
    {
        // Arrange
        var packageRequest = new CtsPackageRequest(Guid.NewGuid());

        // Act
        var savedEntity = await _packageRequestRepository.InsertAsync(packageRequest, autoSave: true);

        // Assert
        savedEntity.ShouldNotBeNull();
        savedEntity.Id.ShouldNotBe(Guid.Empty);

        // Verify it exists in database
        var entityFromDb = await _packageRequestRepository.GetAsync(savedEntity.Id);
        entityFromDb.ShouldNotBeNull();
        entityFromDb.Id.ShouldBe(savedEntity.Id);
    }

    [Fact]
    public async Task Update_CtsPackageRequest_Should_Persist_Changes_To_Database()
    {
        // Arrange
        var packageRequest = new CtsPackageRequest(Guid.NewGuid());
        var savedEntity = await _packageRequestRepository.InsertAsync(packageRequest, autoSave: true);

        // Act - Update the entity
        // Note: CtsPackageRequest properties would be updated here in a real scenario
        await _packageRequestRepository.UpdateAsync(savedEntity, autoSave: true);

        // Assert
        var updatedEntity = await _packageRequestRepository.GetAsync(savedEntity.Id);
        updatedEntity.ShouldNotBeNull();
        updatedEntity.Id.ShouldBe(savedEntity.Id);
    }

    [Fact]
    public async Task Delete_CtsPackageRequest_Should_Remove_From_Database()
    {
        // Arrange
        var packageRequest = new CtsPackageRequest(Guid.NewGuid());
        var savedEntity = await _packageRequestRepository.InsertAsync(packageRequest, autoSave: true);

        // Act
        await _packageRequestRepository.DeleteAsync(savedEntity.Id, autoSave: true);

        // Assert
        var exists = await _packageRequestRepository.FindAsync(savedEntity.Id);
        exists.ShouldBeNull();
    }

    [Fact]
    public async Task GetList_CtsPackageRequests_Should_Return_All_Entities()
    {
        // Arrange
        var packageRequest1 = new CtsPackageRequest(Guid.NewGuid());
        var packageRequest2 = new CtsPackageRequest(Guid.NewGuid());
        var packageRequest3 = new CtsPackageRequest(Guid.NewGuid());

        await _packageRequestRepository.InsertAsync(packageRequest1, autoSave: true);
        await _packageRequestRepository.InsertAsync(packageRequest2, autoSave: true);
        await _packageRequestRepository.InsertAsync(packageRequest3, autoSave: true);

        // Act
        var allEntities = await _packageRequestRepository.GetListAsync();

        // Assert
        allEntities.ShouldNotBeNull();
        allEntities.Count.ShouldBeGreaterThanOrEqualTo(3);
        allEntities.ShouldContain(x => x.Id == packageRequest1.Id);
        allEntities.ShouldContain(x => x.Id == packageRequest2.Id);
        allEntities.ShouldContain(x => x.Id == packageRequest3.Id);
    }

    [Fact]
    public async Task GetCount_CtsPackageRequests_Should_Return_Correct_Count()
    {
        // Arrange
        var initialCount = await _packageRequestRepository.GetCountAsync();

        var packageRequest1 = new CtsPackageRequest(Guid.NewGuid());
        var packageRequest2 = new CtsPackageRequest(Guid.NewGuid());

        await _packageRequestRepository.InsertAsync(packageRequest1, autoSave: true);
        await _packageRequestRepository.InsertAsync(packageRequest2, autoSave: true);

        // Act
        var finalCount = await _packageRequestRepository.GetCountAsync();

        // Assert
        finalCount.ShouldBe(initialCount + 2);
    }

    #endregion

    #region Certificate Database Operations Tests

    [Fact]
    public async Task Create_BahamasCertificate_Should_Persist_To_Database()
    {
        // Arrange
        var certificate = new BahamasCertificate(Guid.NewGuid())
        {
            PublicKey = "test-public-key",
            CertificateContent = Convert.ToBase64String(Encoding.UTF8.GetBytes("test-certificate")),
            CertificateContentType = "application/x-pkcs12",
            CertificateFileName = "test-cert.pfx",
            CertificatePassword = "test-password",
            ExpiredAt = DateTime.UtcNow.AddYears(1),
            ValidFrom = DateTime.UtcNow,
            IsActive = true
        };

        // Act
        var savedCertificate = await _certificateRepository.InsertAsync(certificate, autoSave: true);

        // Assert
        savedCertificate.ShouldNotBeNull();
        savedCertificate.Id.ShouldNotBe(Guid.Empty);

        // Verify it exists in database
        var certFromDb = await _certificateRepository.GetAsync(savedCertificate.Id);
        certFromDb.ShouldNotBeNull();
        certFromDb.PublicKey.ShouldBe("test-public-key");
        certFromDb.IsActive.ShouldBeTrue();
    }

    [Fact]
    public async Task Update_BahamasCertificate_Should_Persist_Changes()
    {
        // Arrange
        var certificate = await CreateTestCertificateInDatabase();

        // Act
        certificate.IsActive = false;
        certificate.CertificatePassword = "updated-password";
        await _certificateRepository.UpdateAsync(certificate, autoSave: true);

        // Assert
        var updatedCert = await _certificateRepository.GetAsync(certificate.Id);
        updatedCert.IsActive.ShouldBeFalse();
        updatedCert.CertificatePassword.ShouldBe("updated-password");
    }

    [Fact]
    public async Task Query_BahamasCertificates_By_ValidDate_Should_Return_Correct_Results()
    {
        // Arrange
        var now = DateTime.UtcNow;
        var activeCert = await CreateTestCertificateInDatabase(validFrom: now.AddDays(-30), expiredAt: now.AddDays(30));
        var expiredCert = await CreateTestCertificateInDatabase(validFrom: now.AddDays(-60), expiredAt: now.AddDays(-30));
        var futureCert = await CreateTestCertificateInDatabase(validFrom: now.AddDays(30), expiredAt: now.AddDays(60));

        // Act
        var validCertificates = await _certificateRepository.GetListAsync(
            x => x.ValidFrom <= now && x.ExpiredAt >= now);

        // Assert
        validCertificates.ShouldNotBeNull();
        validCertificates.ShouldContain(x => x.Id == activeCert.Id);
        validCertificates.ShouldNotContain(x => x.Id == expiredCert.Id);
        validCertificates.ShouldNotContain(x => x.Id == futureCert.Id);
    }

    [Fact]
    public async Task Query_BahamasCertificates_By_IsActive_Should_Filter_Correctly()
    {
        // Arrange
        var activeCert = await CreateTestCertificateInDatabase(isActive: true);
        var inactiveCert = await CreateTestCertificateInDatabase(isActive: false);

        // Act
        var activeCertificates = await _certificateRepository.GetListAsync(x => x.IsActive);

        // Assert
        activeCertificates.ShouldNotBeNull();
        activeCertificates.ShouldContain(x => x.Id == activeCert.Id);
        activeCertificates.ShouldNotContain(x => x.Id == inactiveCert.Id);
    }

    #endregion

    #region Transaction and Concurrency Tests

    [Fact]
    public async Task Concurrent_Inserts_Should_Handle_Database_Constraints()
    {
        // Arrange & Act
        var tasks = new List<Task<CtsPackageRequest>>();
        for (int i = 0; i < 5; i++)
        {
            tasks.Add(Task.Run(async () =>
            {
                var packageRequest = new CtsPackageRequest(Guid.NewGuid());
                return await _packageRequestRepository.InsertAsync(packageRequest, autoSave: true);
            }));
        }

        var results = await Task.WhenAll(tasks);

        // Assert
        results.Length.ShouldBe(5);
        foreach (var result in results)
        {
            result.ShouldNotBeNull();
            result.Id.ShouldNotBe(Guid.Empty);
        }

        // Verify all entities exist in database
        var allEntities = await _packageRequestRepository.GetListAsync();
        foreach (var result in results)
        {
            allEntities.ShouldContain(x => x.Id == result.Id);
        }
    }

    [Fact]
    public async Task Transaction_Rollback_Should_Not_Persist_Changes()
    {
        // Arrange
        var initialCount = await _packageRequestRepository.GetCountAsync();

        // Act & Assert
        await Should.ThrowAsync<Exception>(async () =>
        {
            await WithUnitOfWorkAsync(async () =>
            {
                var packageRequest = new CtsPackageRequest(Guid.NewGuid());
                await _packageRequestRepository.InsertAsync(packageRequest);

                // Force an exception to trigger rollback
                throw new Exception("Test exception to trigger rollback");
            });
        });

        // Assert - No new entities should be persisted
        var finalCount = await _packageRequestRepository.GetCountAsync();
        finalCount.ShouldBe(initialCount);
    }

    [Fact]
    public async Task Bulk_Operations_Should_Handle_Large_Datasets()
    {
        // Arrange
        var packageRequests = new List<CtsPackageRequest>();
        for (int i = 0; i < 100; i++)
        {
            packageRequests.Add(new CtsPackageRequest(Guid.NewGuid()));
        }

        // Act
        await _packageRequestRepository.InsertManyAsync(packageRequests, autoSave: true);

        // Assert
        var count = await _packageRequestRepository.GetCountAsync();
        count.ShouldBeGreaterThanOrEqualTo(100);

        // Verify we can query the bulk inserted data
        var allEntities = await _packageRequestRepository.GetListAsync();
        foreach (var request in packageRequests)
        {
            allEntities.ShouldContain(x => x.Id == request.Id);
        }
    }

    #endregion

    #region Complex Query and Filtering Tests

    [Fact]
    public async Task Query_CtsPackageRequests_With_Complex_Filters_Should_Return_Correct_Results()
    {
        // Arrange
        var request1 = new CtsPackageRequest(Guid.NewGuid());
        var request2 = new CtsPackageRequest(Guid.NewGuid());
        var request3 = new CtsPackageRequest(Guid.NewGuid());

        await _packageRequestRepository.InsertAsync(request1, autoSave: true);
        await _packageRequestRepository.InsertAsync(request2, autoSave: true);
        await _packageRequestRepository.InsertAsync(request3, autoSave: true);

        // Act - Query with specific IDs
        var specificRequests = await _packageRequestRepository.GetListAsync(
            x => x.Id == request1.Id || x.Id == request3.Id);

        // Assert
        specificRequests.Count.ShouldBe(2);
        specificRequests.ShouldContain(x => x.Id == request1.Id);
        specificRequests.ShouldContain(x => x.Id == request3.Id);
        specificRequests.ShouldNotContain(x => x.Id == request2.Id);
    }

    [Fact]
    public async Task Query_Certificates_With_Date_Range_Should_Filter_Correctly()
    {
        // Arrange
        var baseDate = DateTime.UtcNow;
        var cert1 = await CreateTestCertificateInDatabase(
            validFrom: baseDate.AddDays(-10),
            expiredAt: baseDate.AddDays(10));
        var cert2 = await CreateTestCertificateInDatabase(
            validFrom: baseDate.AddDays(-5),
            expiredAt: baseDate.AddDays(15));
        var cert3 = await CreateTestCertificateInDatabase(
            validFrom: baseDate.AddDays(5),
            expiredAt: baseDate.AddDays(20));

        // Act - Query certificates valid at a specific date
        var queryDate = baseDate;
        var validCerts = await _certificateRepository.GetListAsync(
            x => x.ValidFrom <= queryDate && x.ExpiredAt >= queryDate);

        // Assert
        validCerts.ShouldContain(x => x.Id == cert1.Id);
        validCerts.ShouldContain(x => x.Id == cert2.Id);
        validCerts.ShouldNotContain(x => x.Id == cert3.Id);
    }

    [Fact]
    public async Task Paged_Query_Should_Return_Correct_Results()
    {
        // Arrange
        var requests = new List<CtsPackageRequest>();
        for (int i = 0; i < 25; i++)
        {
            requests.Add(new CtsPackageRequest(Guid.NewGuid()));
        }
        await _packageRequestRepository.InsertManyAsync(requests, autoSave: true);

        // Act - Get first page
        var firstPage = await _packageRequestRepository.GetPagedListAsync(0, 10, "Id", false);

        // Assert
        firstPage.ShouldNotBeNull();
        firstPage.Count.ShouldBe(10); // Fixed: removed .Items

        // Act - Get second page
        var secondPage = await _packageRequestRepository.GetPagedListAsync(10, 10, "Id", false);

        // Assert
        secondPage.ShouldNotBeNull();
        secondPage.Count.ShouldBe(10);

        // Verify no overlap between pages
        var firstPageIds = firstPage.Select(x => x.Id).ToList();
        var secondPageIds = secondPage.Select(x => x.Id).ToList();
        firstPageIds.Intersect(secondPageIds).ShouldBeEmpty();
    }

    #endregion

    #region Performance and Edge Case Tests

    [Fact]
    public async Task Large_Dataset_Operations_Should_Complete_Within_Reasonable_Time()
    {
        // Arrange
        var startTime = DateTime.UtcNow;
        var largeDataset = new List<CtsPackageRequest>();
        for (int i = 0; i < 1000; i++)
        {
            largeDataset.Add(new CtsPackageRequest(Guid.NewGuid()));
        }

        // Act
        await _packageRequestRepository.InsertManyAsync(largeDataset, autoSave: true);
        var retrievedData = await _packageRequestRepository.GetListAsync();

        // Assert
        var endTime = DateTime.UtcNow;
        var duration = endTime - startTime;

        retrievedData.Count.ShouldBeGreaterThanOrEqualTo(1000);
        duration.TotalSeconds.ShouldBeLessThan(30); // Should complete within 30 seconds
    }

    [Fact]
    public async Task Empty_Database_Queries_Should_Return_Empty_Results()
    {
        // Arrange - Ensure database is clean for this test
        var allRequests = await _packageRequestRepository.GetListAsync();
        await _packageRequestRepository.DeleteManyAsync(allRequests, autoSave: true);

        // Act
        var emptyResult = await _packageRequestRepository.GetListAsync();
        var zeroCount = await _packageRequestRepository.GetCountAsync();

        // Assert
        emptyResult.ShouldNotBeNull();
        emptyResult.ShouldBeEmpty();
        zeroCount.ShouldBe(0);
    }

    [Fact]
    public async Task Database_Constraints_Should_Be_Enforced()
    {
        // Arrange
        var certificate = new BahamasCertificate(Guid.NewGuid())
        {
            PublicKey = "test-key",
            CertificateContent = "test-content",
            CertificateContentType = "application/x-pkcs12",
            CertificateFileName = "test.pfx",
            ExpiredAt = DateTime.UtcNow.AddYears(1),
            ValidFrom = DateTime.UtcNow,
            IsActive = true
        };

        // Act & Assert - Should enforce required fields
        var savedCert = await _certificateRepository.InsertAsync(certificate, autoSave: true);
        savedCert.ShouldNotBeNull();
        savedCert.PublicKey.ShouldNotBeNullOrEmpty();
        savedCert.CertificateContent.ShouldNotBeNullOrEmpty();
    }

    [Fact]
    public async Task Delete_Should_Remove_Entity_From_Database()
    {
        // Arrange
        var certificate = await CreateTestCertificateInDatabase();

        // Act - Delete
        await _certificateRepository.DeleteAsync(certificate.Id, autoSave: true);

        // Assert - Should not be found in queries
        var deletedCert = await _certificateRepository.FindAsync(certificate.Id);
        deletedCert.ShouldBeNull();
    }

    #endregion

    #region Data Integrity and Validation Tests

    [Fact]
    public async Task Database_Should_Enforce_Entity_Constraints()
    {
        // Arrange & Act & Assert - Test that required fields are enforced
        var certificate = new BahamasCertificate(Guid.NewGuid())
        {
            PublicKey = "test-key",
            CertificateContent = "test-content",
            CertificateContentType = "application/x-pkcs12",
            CertificateFileName = "test.pfx",
            ExpiredAt = DateTime.UtcNow.AddYears(1),
            ValidFrom = DateTime.UtcNow,
            IsActive = true
        };

        var savedCert = await _certificateRepository.InsertAsync(certificate, autoSave: true);
        savedCert.ShouldNotBeNull();
        savedCert.PublicKey.ShouldNotBeNullOrEmpty();
        savedCert.CertificateContent.ShouldNotBeNullOrEmpty();
    }

    [Fact]
    public async Task Multiple_PackageRequests_Should_Be_Handled_In_Database()
    {
        // Arrange
        var packageRequest1 = await CreateTestPackageRequestInDatabase();
        var packageRequest2 = await CreateTestPackageRequestInDatabase();
        var packageRequest3 = await CreateTestPackageRequestInDatabase();

        // Act - Verify all entities exist in database
        var allRequests = await _packageRequestRepository.GetListAsync();

        // Assert
        allRequests.Count.ShouldBeGreaterThanOrEqualTo(3);
        allRequests.ShouldContain(x => x.Id == packageRequest1.Id);
        allRequests.ShouldContain(x => x.Id == packageRequest2.Id);
        allRequests.ShouldContain(x => x.Id == packageRequest3.Id);
    }

    [Fact]
    public async Task Database_Should_Handle_Multiple_Countries_Data()
    {
        // Arrange
        var testCountries = new[] { "US", "CA", "GB", "DE", "FR" };
        var certificates = new List<BahamasCertificate>();

        foreach (var country in testCountries)
        {
            var certificate = await CreateTestCertificateInDatabase();
            certificates.Add(certificate);
        }

        // Act
        var allCertificates = await _certificateRepository.GetListAsync();

        // Assert
        allCertificates.Count.ShouldBeGreaterThanOrEqualTo(testCountries.Length);
        foreach (var cert in certificates)
        {
            allCertificates.ShouldContain(x => x.Id == cert.Id);
        }
    }

    [Fact]
    public async Task Database_Should_Track_Entity_Timestamps_Correctly()
    {
        // Arrange
        var beforeCreate = DateTime.UtcNow.AddSeconds(-1);
        var packageRequest = await CreateTestPackageRequestInDatabase();
        var afterCreate = DateTime.UtcNow.AddSeconds(1);

        // Act
        var entityFromDb = await _packageRequestRepository.GetAsync(packageRequest.Id);

        // Assert
        entityFromDb.CreationTime.ShouldBeGreaterThan(beforeCreate);
        entityFromDb.CreationTime.ShouldBeLessThan(afterCreate);
    }

    [Fact]
    public async Task Database_Should_Maintain_Data_Integrity_During_Operations()
    {
        // Arrange
        var initialCount = await _packageRequestRepository.GetCountAsync();
        var packageRequest = await CreateTestPackageRequestInDatabase();

        // Act
        var finalCount = await _packageRequestRepository.GetCountAsync();

        // Assert - Verify data integrity is maintained
        finalCount.ShouldBe(initialCount + 1);

        // Verify entity exists and is accessible
        var entityInDb = await _packageRequestRepository.GetAsync(packageRequest.Id);
        entityInDb.ShouldNotBeNull();
        entityInDb.Id.ShouldBe(packageRequest.Id);
    }

    [Fact]
    public async Task Database_Should_Handle_Transaction_Rollback_Correctly()
    {
        // Arrange
        var initialCount = await _packageRequestRepository.GetCountAsync();

        // Act & Assert - Test transaction rollback
        await Should.ThrowAsync<Exception>(async () =>
        {
            await WithUnitOfWorkAsync(async () =>
            {
                var packageRequest = new CtsPackageRequest(Guid.NewGuid());
                await _packageRequestRepository.InsertAsync(packageRequest);

                // Force an exception to trigger rollback
                throw new Exception("Test exception to trigger rollback");
            });
        });

        // Assert - No new entities should be persisted due to rollback
        var finalCount = await _packageRequestRepository.GetCountAsync();
        finalCount.ShouldBe(initialCount);
    }

    [Fact]
    public async Task Database_Should_Handle_Complex_Entity_Relationships()
    {
        // Arrange - Create related entities
        var certificate = await CreateTestCertificateInDatabase();
        var packageRequest = await CreateTestPackageRequestInDatabase();

        // Act - Query both entities
        var certFromDb = await _certificateRepository.GetAsync(certificate.Id);
        var requestFromDb = await _packageRequestRepository.GetAsync(packageRequest.Id);

        // Assert - Both entities should exist independently
        certFromDb.ShouldNotBeNull();
        certFromDb.Id.ShouldBe(certificate.Id);

        requestFromDb.ShouldNotBeNull();
        requestFromDb.Id.ShouldBe(packageRequest.Id);
    }

    [Fact]
    public async Task Database_Should_Handle_Concurrent_Access_Safely()
    {
        // Arrange & Act - Simulate concurrent database operations
        var tasks = new List<Task<CtsPackageRequest>>();
        for (int i = 0; i < 5; i++)
        {
            tasks.Add(Task.Run(async () =>
            {
                var packageRequest = new CtsPackageRequest(Guid.NewGuid());
                return await _packageRequestRepository.InsertAsync(packageRequest, autoSave: true);
            }));
        }

        var results = await Task.WhenAll(tasks);

        // Assert - All operations should complete successfully
        results.Length.ShouldBe(5);
        foreach (var result in results)
        {
            result.ShouldNotBeNull();
            result.Id.ShouldNotBe(Guid.Empty);
        }

        // Verify entity integrity in database
        var allEntities = await _packageRequestRepository.GetListAsync();
        foreach (var result in results)
        {
            allEntities.ShouldContain(x => x.Id == result.Id);
        }
    }

    [Fact]
    public async Task Database_Should_Handle_Different_Data_Types_Correctly()
    {
        // Arrange - Test with different certificate configurations
        var certificates = new List<BahamasCertificate>();
        var encodings = new[] { "UTF-8", "UTF-16", "ISO-8859-1" };

        foreach (var encoding in encodings)
        {
            var certificate = await CreateTestCertificateInDatabase();
            certificates.Add(certificate);
        }

        // Act
        var allCertificates = await _certificateRepository.GetListAsync();

        // Assert - Should handle different certificate types
        allCertificates.Count.ShouldBeGreaterThanOrEqualTo(encodings.Length);
        foreach (var cert in certificates)
        {
            allCertificates.ShouldContain(x => x.Id == cert.Id);
        }
    }

    [Fact]
    public async Task Database_Should_Maintain_Data_Consistency_Across_Operations()
    {
        // Arrange
        var packageRequests = new List<CtsPackageRequest>();
        for (int i = 0; i < 10; i++)
        {
            packageRequests.Add(await CreateTestPackageRequestInDatabase());
        }

        // Act - Verify all entities exist in database
        var allEntitiesInDb = await _packageRequestRepository.GetListAsync();

        // Assert - Verify data consistency
        allEntitiesInDb.Count.ShouldBeGreaterThanOrEqualTo(10);

        foreach (var request in packageRequests)
        {
            var entityInDb = await _packageRequestRepository.GetAsync(request.Id);
            entityInDb.ShouldNotBeNull();
            entityInDb.Id.ShouldBe(request.Id);
        }
    }

    #endregion

    #region Edge Cases and Boundary Tests

    [Fact]
    public async Task Database_Should_Handle_Empty_Result_Sets()
    {
        // Arrange - Ensure database is clean for this test
        var allRequests = await _packageRequestRepository.GetListAsync();
        await _packageRequestRepository.DeleteManyAsync(allRequests, autoSave: true);

        // Act
        var emptyResult = await _packageRequestRepository.GetListAsync();
        var zeroCount = await _packageRequestRepository.GetCountAsync();

        // Assert
        emptyResult.ShouldNotBeNull();
        emptyResult.ShouldBeEmpty();
        zeroCount.ShouldBe(0);
    }

    [Fact]
    public async Task Database_Should_Handle_Large_Data_Sets()
    {
        // Arrange
        var largeDataset = new List<CtsPackageRequest>();
        for (int i = 0; i < 100; i++)
        {
            largeDataset.Add(new CtsPackageRequest(Guid.NewGuid()));
        }

        // Act
        await _packageRequestRepository.InsertManyAsync(largeDataset, autoSave: true);
        var retrievedData = await _packageRequestRepository.GetListAsync();

        // Assert
        retrievedData.Count.ShouldBeGreaterThanOrEqualTo(100);
        foreach (var request in largeDataset)
        {
            retrievedData.ShouldContain(x => x.Id == request.Id);
        }
    }

    [Fact]
    public async Task Database_Should_Handle_Null_And_Default_Values()
    {
        // Arrange
        var certificate = new BahamasCertificate(Guid.NewGuid())
        {
            PublicKey = "test-key",
            CertificateContent = "test-content",
            CertificateContentType = "application/x-pkcs12",
            CertificateFileName = "test.pfx",
            CertificatePassword = null, // Null password
            ExpiredAt = DateTime.UtcNow.AddYears(1),
            ValidFrom = DateTime.UtcNow,
            IsActive = false // Default false
        };

        // Act
        var savedCert = await _certificateRepository.InsertAsync(certificate, autoSave: true);

        // Assert - Should handle null and default values gracefully
        savedCert.ShouldNotBeNull();
        savedCert.CertificatePassword.ShouldBeNull();
        savedCert.IsActive.ShouldBeFalse();
    }

    #endregion

    #region Helper Methods

    /// <summary>
    /// Creates a test certificate in the database with customizable properties
    /// </summary>
    private async Task<BahamasCertificate> CreateTestCertificateInDatabase(
        DateTime? validFrom = null,
        DateTime? expiredAt = null,
        bool isActive = true)
    {
        var now = DateTime.UtcNow;
        var certificate = new BahamasCertificate(Guid.NewGuid())
        {
            PublicKey = $"test-public-key-{Guid.NewGuid()}",
            CertificateContent = Convert.ToBase64String(Encoding.UTF8.GetBytes($"test-certificate-{Guid.NewGuid()}")),
            CertificateContentType = "application/x-pkcs12",
            CertificateFileName = $"test-cert-{Guid.NewGuid()}.pfx",
            CertificatePassword = "test-password",
            ValidFrom = validFrom ?? now.AddDays(-30),
            ExpiredAt = expiredAt ?? now.AddDays(30),
            IsActive = isActive
        };

        return await _certificateRepository.InsertAsync(certificate, autoSave: true);
    }

    /// <summary>
    /// Creates a test package request in the database
    /// </summary>
    private async Task<CtsPackageRequest> CreateTestPackageRequestInDatabase()
    {
        var packageRequest = new CtsPackageRequest(Guid.NewGuid());
        return await _packageRequestRepository.InsertAsync(packageRequest, autoSave: true);
    }

    /// <summary>
    /// Cleans up test data from the database
    /// </summary>
    private async Task CleanupTestDataAsync()
    {
        var allRequests = await _packageRequestRepository.GetListAsync();
        if (allRequests.Any())
        {
            await _packageRequestRepository.DeleteManyAsync(allRequests, autoSave: true);
        }

        var allCertificates = await _certificateRepository.GetListAsync();
        if (allCertificates.Any())
        {
            await _certificateRepository.DeleteManyAsync(allCertificates, autoSave: true);
        }
    }

    /// <summary>
    /// Seeds test data for complex scenarios
    /// </summary>
    private async Task<(List<CtsPackageRequest> requests, List<BahamasCertificate> certificates)> SeedComplexTestDataAsync()
    {
        var requests = new List<CtsPackageRequest>();
        var certificates = new List<BahamasCertificate>();

        // Create multiple package requests
        for (int i = 0; i < 10; i++)
        {
            var request = new CtsPackageRequest(Guid.NewGuid());
            requests.Add(await _packageRequestRepository.InsertAsync(request, autoSave: true));
        }

        // Create multiple certificates with different validity periods
        var now = DateTime.UtcNow;
        for (int i = 0; i < 5; i++)
        {
            var cert = await CreateTestCertificateInDatabase(
                validFrom: now.AddDays(-30 + (i * 10)),
                expiredAt: now.AddDays(30 + (i * 10)),
                isActive: i % 2 == 0);
            certificates.Add(cert);
        }

        return (requests, certificates);
    }

    #endregion
}
